const ffmpeg = require('fluent-ffmpeg');
const { PDFDocument } = require('pdf-lib');
const fs = require('fs');
const path = require('path');
const { getS3Instance, isUsingS3Storage } = require('./storageHelper');

// Configure ffmpeg path using ffmpeg-static for all environments
// This ensures FFmpeg is available even if not installed system-wide
try {
  const ffmpegStatic = require('ffmpeg-static');
  if (ffmpegStatic) {
    ffmpeg.setFfmpegPath(ffmpegStatic);
    console.log(`[Setup] FFmpeg path set to: ${ffmpegStatic}`);
  } else {
    console.warn('[Setup] ffmpeg-static returned null, will try to use system FFmpeg');
  }
} catch (error) {
  console.warn('[Setup] Failed to load ffmpeg-static, will try to use system FFmpeg:', error.message);
}

// Get S3 instance if credentials are available (cached instance)
const s3 = getS3Instance();

/**
 * Determine optimal download strategy for video preview generation (optimized for slow connections)
 * @param {number} fileSizeBytes - File size in bytes
 * @returns {Object} - Strategy object with type and parameters
 */
const getOptimalDownloadStrategy = (fileSizeBytes) => {
  const fileSizeMB = Math.round(fileSizeBytes / (1024 * 1024));

  // Strategy thresholds optimized for slow internet connections
  const SMALL_FILE_THRESHOLD = 15 * 1024 * 1024;  // 15MB
  const MEDIUM_FILE_THRESHOLD = 200 * 1024 * 1024; // 200MB

  // Maximum timeout for all operations (3 minutes for slow connections)
  const MAX_TIMEOUT = 180000; // 3 minutes (180 seconds)

  if (fileSizeBytes <= SMALL_FILE_THRESHOLD) {
    // Small files (≤15MB): Download entire file
    // Full download is still efficient for very small files
    return {
      type: 'full',
      reason: `Small file (${fileSizeMB}MB) - full download for optimal quality`,
      timeout: MAX_TIMEOUT,
      downloadSize: fileSizeMB,
      downloadSizeBytes: fileSizeBytes
    };
  } else if (fileSizeBytes <= MEDIUM_FILE_THRESHOLD) {
    // Medium files (15MB-200MB): Download first 15MB only
    // 15MB provides sufficient content for 10-second preview across most bitrates
    const rangeSize = 15 * 1024 * 1024; // Fixed 15MB for medium files
    return {
      type: 'range',
      reason: `Medium file (${fileSizeMB}MB) - downloading first 15MB for slow connections`,
      timeout: MAX_TIMEOUT,
      rangeSize: rangeSize,
      downloadSize: 15,
      downloadSizeBytes: rangeSize
    };
  } else {
    // Large files (>200MB): Download first 20MB only
    // 20MB provides good quality preview while minimizing download time
    const rangeSize = 20 * 1024 * 1024; // Fixed 20MB for large files
    return {
      type: 'range',
      reason: `Large file (${fileSizeMB}MB) - downloading first 20MB for slow connections`,
      timeout: MAX_TIMEOUT,
      rangeSize: rangeSize,
      downloadSize: 20,
      downloadSizeBytes: rangeSize
    };
  }
};

/**
 * Generate video preview (30-second clip from the beginning) - Full video processing
 * @param {string} inputPath - Path to the original video file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generateVideoPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  return new Promise((resolve, reject) => {
    try {
      const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

      console.log(`[Preview] Starting video preview generation for: ${outputFileName}`);
      console.log(`[Preview] Input path: ${inputPath}`);
      console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

      if (isS3Upload && s3) {
        // For S3 uploads, use the optimized preview generation
        generateVideoPreviewS3Optimized(inputPath, outputFileName)
          .then((result) => {
            console.log(`[Preview] S3 video preview generated successfully: ${result}`);
            resolve(result);
          })
          .catch((error) => {
            console.error(`[Preview] S3 video preview generation failed:`, error);
            reject(error);
          });
      } else {
        // Local file processing
        const outputPath = path.join('./uploads/previews/', previewFileName);

        // Ensure preview directory exists
        const previewDir = path.dirname(outputPath);
        if (!fs.existsSync(previewDir)) {
          console.log(`[Preview] Creating preview directory: ${previewDir}`);
          fs.mkdirSync(previewDir, { recursive: true });
        }

        // Validate input file exists
        if (!fs.existsSync(inputPath)) {
          const error = new Error(`Input video file not found: ${inputPath}`);
          console.error(`[Preview] ${error.message}`);
          reject(error);
          return;
        }

        console.log(`[Preview] Processing local video file to: ${outputPath}`);

        // Add timeout for FFmpeg processing
        const ffmpegTimeout = setTimeout(() => {
          console.error(`[Preview] FFmpeg timeout after 3 minutes for: ${outputFileName}`);
          reject(new Error('Video preview generation timeout'));
        }, 180000); // 3 minutes timeout

        ffmpeg(inputPath)
          .setStartTime(0) // Start from beginning
          .setDuration(10) // 10 seconds duration for better preview
          .videoCodec('libx264') // Ensure compatibility
          .audioCodec('aac') // Ensure audio compatibility
          .format('mp4') // Ensure MP4 format
          .size('?x480') // Reduce resolution to 480p for smaller file size
          .videoBitrate('500k') // Reduce bitrate for smaller file size
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] Processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(ffmpegTimeout);
            const previewUrl = `/uploads/previews/${previewFileName}`;
            console.log(`[Preview] Video preview generated successfully: ${previewUrl}`);

            // Verify the output file was created and has content
            if (fs.existsSync(outputPath)) {
              const stats = fs.statSync(outputPath);
              console.log(`[Preview] Preview file size: ${stats.size} bytes`);
              if (stats.size > 0) {
                resolve(previewUrl);
              } else {
                reject(new Error('Generated preview file is empty'));
              }
            } else {
              reject(new Error('Preview file was not created'));
            }
          })
          .on('error', (err) => {
            clearTimeout(ffmpegTimeout);
            console.error(`[Preview] FFmpeg error:`, err);
            reject(new Error(`Failed to generate video preview: ${err.message}`));
          })
          .run();
      }
    } catch (error) {
      console.error(`[Preview] Error in generateVideoPreview:`, error);
      reject(error);
    }
  });
};

/**
 * Generate video preview asynchronously (non-blocking) with database updates
 * @param {string} inputPath - Path to the original video file
 * @param {string} fileName - Original file name
 * @param {boolean} isS3Upload - Whether file is stored on S3
 * @param {string} contentId - Optional content ID for database updates
 * @returns {Promise<string>} - URL of the generated preview
 */
const generateVideoPreviewAsync = async (inputPath, fileName, isS3Upload = false, contentId = null) => {
  try {
    console.log(`[Preview] Starting async preview generation for: ${fileName}`);

    let previewUrl;
    if (isS3Upload) {
      // Use optimized S3 preview generation with range requests
      previewUrl = await generateVideoPreviewS3Optimized(inputPath, fileName);
    } else {
      // Use regular local file processing
      previewUrl = await generateVideoPreview(inputPath, fileName, false);
    }

    // Update database if contentId is provided
    if (contentId && previewUrl) {
      try {
        await updateContentPreviewStatus(contentId, previewUrl, 'completed');
        console.log(`[Preview] Database updated for content ${contentId} with preview URL: ${previewUrl}`);
      } catch (dbError) {
        console.error(`[Preview] Failed to update database for content ${contentId}:`, dbError);
      }
    }

    return previewUrl;
  } catch (error) {
    console.error(`[Preview] Async preview generation failed for ${fileName}:`, error);

    // Update database with failed status if contentId is provided
    if (contentId) {
      try {
        await updateContentPreviewStatus(contentId, null, 'failed', error.message);
        console.log(`[Preview] Database updated for content ${contentId} with failed status`);
      } catch (dbError) {
        console.error(`[Preview] Failed to update database with error status for content ${contentId}:`, dbError);
      }
    }

    throw error;
  }
};

/**
 * Update content preview status in database
 * @param {string} contentId - Content ID
 * @param {string} previewUrl - Preview URL (null for failed)
 * @param {string} status - Preview status ('completed' or 'failed')
 * @param {string} error - Error message (for failed status)
 */
const updateContentPreviewStatus = async (contentId, previewUrl, status, error = null) => {
  try {
    const Content = require('../models/Content');

    const updateData = {
      previewStatus: status,
      previewError: error
    };

    if (previewUrl) {
      updateData.previewUrl = previewUrl;
    }

    await Content.findByIdAndUpdate(contentId, updateData);
    console.log(`[Preview] Content ${contentId} preview status updated to: ${status}`);
  } catch (error) {
    console.error(`[Preview] Error updating content preview status:`, error);
    throw error;
  }
};

/**
 * Generate video preview for S3 stored files - Smart optimization for all video sizes
 * @param {string} s3Url - S3 URL of the original video
 * @param {string} fileName - Original file name
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generateVideoPreviewS3Optimized = async (s3Url, fileName) => {
  return new Promise(async (resolve, reject) => {
    try {
      const previewFileName = `${path.parse(fileName).name}_preview${path.parse(fileName).ext}`;

      // Create temporary local paths
      const tempDir = './temp';
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempInputPath = path.join(tempDir, `input_${Date.now()}.mp4`);
      const tempOutputPath = path.join(tempDir, `preview_${Date.now()}.mp4`);

      console.log(`[Preview] Starting smart S3 preview generation for: ${fileName}`);

      // Download the file from S3
      const s3 = getS3Instance();
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract S3 key from URL
      let key;
      try {
        key = validateAndExtractS3Key(s3Url, bucketName);
      } catch (keyError) {
        console.error('[Preview] Error extracting S3 key:', keyError);
        reject(new Error(`Invalid S3 URL format: ${keyError.message}`));
        return;
      }

      // First, get file size to determine optimal download strategy
      let strategy;
      let downloadParams;

      try {
        console.log(`[Preview] Checking file size for: ${key}`);
        const headResult = await s3.headObject({ Bucket: bucketName, Key: key }).promise();
        const fileSize = headResult.ContentLength;

        // Get optimal strategy based on file size
        strategy = getOptimalDownloadStrategy(fileSize);
        console.log(`[Preview] ${strategy.reason}`);

        // Set download parameters based on strategy
        if (strategy.type === 'full') {
          downloadParams = { Bucket: bucketName, Key: key };
        } else {
          downloadParams = {
            Bucket: bucketName,
            Key: key,
            Range: `bytes=0-${strategy.rangeSize - 1}`
          };
        }
      } catch (headError) {
        console.error(`[Preview] Error getting file size for ${key}:`, headError);
        // Fallback strategy for unknown file size (optimized for slow connections)
        const fallbackRangeSize = 20 * 1024 * 1024; // 20MB fallback
        strategy = {
          type: 'range-fallback',
          reason: 'Unknown file size - using 20MB fallback for slow connections',
          timeout: 180000, // 3 minutes
          downloadSize: 20,
          rangeSize: fallbackRangeSize,
          downloadSizeBytes: fallbackRangeSize
        };
        downloadParams = {
          Bucket: bucketName,
          Key: key,
          Range: `bytes=0-${fallbackRangeSize - 1}` // 20MB fallback
        };
        console.log(`[Preview] ${strategy.reason}`);
      }

      const fileStream = fs.createWriteStream(tempInputPath);
      const s3Stream = s3.getObject(downloadParams).createReadStream();

      s3Stream.on('error', (s3Error) => {
        console.error(`[Preview] S3 stream error for key: ${key} (strategy: ${strategy.type})`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          reject(new Error(`File not found in S3: ${key}. The file may have been moved or deleted.`));
        } else if (s3Error.code === 'InvalidRange' && strategy.type.includes('range')) {
          console.log(`[Preview] Range not supported, falling back to full download for: ${key}`);
          // Fallback to full download if range not supported
          const fullDownloadParams = { Bucket: bucketName, Key: key };
          const fullS3Stream = s3.getObject(fullDownloadParams).createReadStream();
          fullS3Stream.pipe(fileStream);
        } else {
          reject(new Error(`S3 stream error: ${s3Error.message}`));
        }
      });

      s3Stream.pipe(fileStream);

      fileStream.on('close', () => {
        console.log(`[Preview] Download completed for ${strategy.type} strategy (${strategy.downloadSize}MB)`);
        console.log(`[Preview] Starting FFmpeg processing with 3-minute timeout for slow connections`);

        // Set timeout based on strategy (3 minutes for slow connections)
        const timeoutDuration = strategy.timeout;
        const s3FfmpegTimeout = setTimeout(() => {
          console.error(`[Preview] S3 FFmpeg timeout after ${timeoutDuration/1000}s for: ${previewFileName}`);
          console.error(`[Preview] Strategy used: ${strategy.type} (${strategy.downloadSize}MB download)`);
          console.error(`[Preview] Consider checking internet connection or video format compatibility`);
          // Clean up temporary files
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanupError) {
            console.error('[Preview] Error cleaning up temp files:', cleanupError);
          }
          reject(new Error(`S3 preview generation timeout after 3 minutes (${strategy.type} strategy, ${strategy.downloadSize}MB)`));
        }, timeoutDuration);

        // Process the downloaded portion
        ffmpeg(tempInputPath)
          .setStartTime(0)
          .setDuration(10) // 10 seconds duration
          .videoCodec('libx264')
          .audioCodec('aac')
          .format('mp4')
          .size('?x480')
          .videoBitrate('500k')
          .output(tempOutputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] FFmpeg processing started (${strategy.type} strategy, ${strategy.downloadSize}MB)`);
            console.log(`[Preview] FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0);
            console.log(`[Preview] Processing ${strategy.downloadSize}MB download: ${percent}% done`);
          })
          .on('end', () => {
            clearTimeout(s3FfmpegTimeout);
            // Upload preview to S3
            const uploadParams = {
              Bucket: bucketName,
              Key: `previews/${previewFileName}`,
              Body: fs.createReadStream(tempOutputPath),
              ContentType: 'video/mp4'
            };

            console.log(`[Preview] Uploading preview to S3 (${strategy.type} strategy): previews/${previewFileName}`);

            s3.upload(uploadParams, (uploadErr, uploadData) => {
              // Clean up temporary files
              try {
                if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
                if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
              } catch (cleanupError) {
                console.error('[Preview] Error cleaning up temp files:', cleanupError);
              }

              if (uploadErr) {
                console.error(`[Preview] Error uploading preview to S3 (${strategy.type} strategy):`, uploadErr);
                reject(new Error(`Failed to upload preview to S3 (${strategy.type} strategy): ${uploadErr.message}`));
              } else {
                console.log(`[Preview] Preview uploaded successfully using ${strategy.type} strategy (${strategy.downloadSize}MB): ${uploadData.Location}`);
                resolve(uploadData.Location);
              }
            });
          })
          .on('error', (err) => {
            clearTimeout(s3FfmpegTimeout);
            console.error(`[Preview] FFmpeg error with ${strategy.type} strategy (${strategy.downloadSize}MB):`, err);
            console.error(`[Preview] This may indicate insufficient video data or format incompatibility`);
            // Clean up temporary files
            try {
              if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
              if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
            } catch (cleanupError) {
              console.error('[Preview] Error cleaning up temp files:', cleanupError);
            }
            reject(new Error(`Failed to generate video preview using ${strategy.type} strategy (${strategy.downloadSize}MB): ${err.message}`));
          })
          .run();
      });

      fileStream.on('error', (err) => {
        console.error(`[Preview] Error downloading from S3 using ${strategy.type} strategy (${strategy.downloadSize}MB):`, err);
        console.error(`[Preview] This may indicate network connectivity issues or S3 access problems`);
        reject(new Error(`Failed to download from S3 using ${strategy.type} strategy: ${err.message}`));
      });

    } catch (error) {
      console.error('Error in generateVideoPreviewS3Optimized:', error);
      reject(error);
    }
  });
};



/**
 * Generate PDF preview (first page only)
 * @param {string} inputPath - Path to the original PDF file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generatePdfPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  try {
    const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

    console.log(`[Preview] Starting PDF preview generation for: ${outputFileName}`);
    console.log(`[Preview] Input path: ${inputPath}`);
    console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

    if (isS3Upload && s3) {
      const result = await generatePdfPreviewS3(inputPath, previewFileName);
      console.log(`[Preview] S3 PDF preview generated successfully: ${result}`);
      return result;
    } else {
      // Local file processing
      const outputPath = path.join('./uploads/previews/', previewFileName);

      // Ensure preview directory exists
      const previewDir = path.dirname(outputPath);
      if (!fs.existsSync(previewDir)) {
        console.log(`[Preview] Creating preview directory: ${previewDir}`);
        fs.mkdirSync(previewDir, { recursive: true });
      }

      // Validate input file exists
      if (!fs.existsSync(inputPath)) {
        const error = new Error(`Input PDF file not found: ${inputPath}`);
        console.error(`[Preview] ${error.message}`);
        throw error;
      }

      console.log(`[Preview] Processing local PDF file to: ${outputPath}`);

      // Read the original PDF
      const existingPdfBytes = fs.readFileSync(inputPath);
      console.log(`[Preview] Original PDF size: ${existingPdfBytes.length} bytes`);

      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      const pageCount = pdfDoc.getPageCount();
      console.log(`[Preview] PDF has ${pageCount} pages, extracting first page`);

      if (pageCount === 0) {
        throw new Error('PDF file has no pages');
      }

      // Create a new PDF with only the first page
      const newPdfDoc = await PDFDocument.create();
      const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
      newPdfDoc.addPage(firstPage);

      // Save the preview PDF
      const pdfBytes = await newPdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);

      console.log(`[Preview] Preview PDF size: ${pdfBytes.length} bytes`);

      // Verify the output file was created and has content
      if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log(`[Preview] Preview file created successfully with size: ${stats.size} bytes`);
        if (stats.size === 0) {
          throw new Error('Generated preview file is empty');
        }
      } else {
        throw new Error('Preview file was not created');
      }

      const previewUrl = `/uploads/previews/${previewFileName}`;
      console.log(`[Preview] PDF preview generated successfully: ${previewUrl}`);
      return previewUrl;
    }
  } catch (error) {
    console.error(`[Preview] Error generating PDF preview:`, error);
    throw new Error(`Failed to generate PDF preview: ${error.message}`);
  }
};

/**
 * Generate PDF preview for S3 stored files
 * @param {string} s3Url - S3 URL of the original PDF
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generatePdfPreviewS3 = async (s3Url, previewFileName) => {
  try {
    // Download original PDF from S3
    const bucketName = process.env.AWS_BUCKET_NAME;

    // Extract and validate the S3 key from the URL
    const key = validateAndExtractS3Key(s3Url, bucketName);

    if (!key) {
      throw new Error(`Failed to extract valid S3 key from PDF URL: ${s3Url}`);
    }

    console.log(`[Preview] Downloading PDF from S3 - Bucket: ${bucketName}, Key: ${key}`);
    console.log(`[Preview] Original PDF S3 URL: ${s3Url}`);
    console.log(`[Preview] Extracted PDF key parts:`, {
      urlParts: s3Url.split('/'),
      bucketName,
      extractedKey: key
    });

    const downloadParams = {
      Bucket: bucketName,
      Key: key
    };

    // Add error handling for S3 PDF download
    let data;
    try {
      data = await s3.getObject(downloadParams).promise();
    } catch (s3Error) {
      console.error(`[Preview] S3 PDF download failed for key: ${key}`, s3Error);
      if (s3Error.code === 'NoSuchKey') {
        throw new Error(`PDF file not found in S3: ${key}. The file may have been moved or deleted.`);
      }
      throw new Error(`S3 PDF download failed: ${s3Error.message}`);
    }

    const existingPdfBytes = data.Body;

    // Process PDF
    const pdfDoc = await PDFDocument.load(existingPdfBytes);
    const newPdfDoc = await PDFDocument.create();
    const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
    newPdfDoc.addPage(firstPage);

    const pdfBytes = await newPdfDoc.save();

    // Upload preview to S3
    const uploadParams = {
      Bucket: bucketName,
      Key: `previews/${previewFileName}`,
      Body: pdfBytes,
      ContentType: 'application/pdf'
      // Removed ACL setting - bucket policy handles public access
    };

    const uploadResult = await s3.upload(uploadParams).promise();
    return uploadResult.Location;

  } catch (error) {
    console.error('Error generating PDF preview for S3:', error);
    throw new Error(`Failed to generate PDF preview for S3: ${error.message}`);
  }
};

/**
 * Generate preview based on content type
 * @param {string} contentType - Type of content (Video, PDF, etc.)
 * @param {string} filePath - Path to the original file
 * @param {string} fileName - Original file name
 * @param {boolean} isS3Upload - Whether file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<string|null>} - URL of the generated preview or null if not supported
 */
const generatePreview = async (contentType, filePath, fileName, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    isS3Upload = isUsingS3Storage();
  }
  try {
    console.log(`[Preview] Starting preview generation for ${contentType} file: ${fileName}`);

    // Get file extension once for all cases
    const fileExt = path.extname(fileName).toLowerCase();

    switch (contentType.toLowerCase()) {
      case 'video':
        // Check if it's a video file by extension
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];

        if (videoExtensions.includes(fileExt)) {
          console.log(`[Preview] Generating video preview for: ${fileName}`);
          return await generateVideoPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Video file extension ${fileExt} not supported for preview generation`);
          return null;
        }

      case 'pdf':
      case 'document':
        if (fileExt === '.pdf') {
          console.log(`[Preview] Generating PDF preview for: ${fileName}`);
          return await generatePdfPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Non-PDF document detected: ${fileName} (${fileExt})`);
          console.log(`[Preview] Document preview will use original file for display`);

          // For non-Office documents, return the original file URL as preview
          if (isS3Upload) {
            // For S3 files, return the S3 URL directly
            console.log(`[Preview] Returning S3 URL for document preview: ${filePath}`);
            return filePath;
          } else {
            // For local files, ensure proper URL format
            // If filePath is already a proper URL path (starts with /uploads), use it
            if (filePath.startsWith('/uploads/')) {
              console.log(`[Preview] Returning existing URL path for document preview: ${filePath}`);
              return filePath;
            } else {
              // If filePath is a local file path, convert to URL format
              const normalizedPath = filePath.replace(/\\/g, '/'); // Convert backslashes to forward slashes
              let urlPath;

              if (normalizedPath.startsWith('./uploads/')) {
                urlPath = normalizedPath.substring(1); // Remove leading dot
              } else if (normalizedPath.startsWith('uploads/')) {
                urlPath = '/' + normalizedPath; // Add leading slash
              } else {
                // Assume it's just the filename and construct the full path
                urlPath = `/uploads/${fileName}`;
              }

              console.log(`[Preview] Converted local path to URL for document preview: ${urlPath}`);
              return urlPath;
            }
          }
        }

      default:
        console.log(`[Preview] Preview generation not supported for content type: ${contentType}`);
        return null;
    }
  } catch (error) {
    console.error(`[Preview] Error in generatePreview for ${fileName}:`, error);
    console.error(`[Preview] Error details:`, {
      contentType,
      fileName,
      filePath,
      isS3Upload,
      errorMessage: error.message,
      errorStack: error.stack
    });
    // Don't throw error - just log it and return null so content creation doesn't fail
    return null;
  }
};



/**
 * Validate file type for preview generation
 * @param {string} contentType - Content type
 * @param {string} fileName - File name
 * @returns {boolean} - Whether preview can be generated
 */
const canGeneratePreview = (contentType, fileName) => {
  const fileExt = path.extname(fileName).toLowerCase();

  switch (contentType.toLowerCase()) {
    case 'video':
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
      return videoExtensions.includes(fileExt);

    case 'pdf':
    case 'document':
      // Support document types - only PDF supported
      const documentExtensions = ['.pdf'];
      return documentExtensions.includes(fileExt);

    default:
      return false;
  }
};

/**
 * Clean up preview files when content is deleted
 * @param {string} previewUrl - URL of the preview file to delete
 * @param {boolean} isS3Upload - Whether the file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<boolean>} - Whether cleanup was successful
 */
const cleanupPreviewFile = async (previewUrl, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    const { isS3Url } = require('./storageHelper');
    isS3Upload = isS3Url(previewUrl);
  }
  try {
    if (!previewUrl) {
      console.log('[Cleanup] No preview URL provided, skipping cleanup');
      return true;
    }

    console.log(`[Cleanup] Starting preview file cleanup for: ${previewUrl}`);

    if (isS3Upload && s3) {
      // S3 cleanup
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract the full S3 key from the preview URL
      let key;
      if (previewUrl.includes('amazonaws.com')) {
        // For standard S3 URLs, extract everything after the bucket name
        const urlParts = previewUrl.split('/');
        const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
        if (bucketIndex !== -1) {
          key = urlParts.slice(bucketIndex + 1).join('/');
        } else {
          // Fallback: assume everything after the domain is the key
          const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
          key = urlParts.slice(domainIndex + 1).join('/');
        }
      } else {
        // For relative URLs like /uploads/previews/filename.ext
        const fileName = previewUrl.split('/').pop();
        key = `previews/${fileName}`;
      }

      const deleteParams = {
        Bucket: bucketName,
        Key: key
      };

      try {
        await s3.deleteObject(deleteParams).promise();
        console.log(`[Cleanup] S3 preview file deleted successfully: ${key}`);
        return true;
      } catch (s3Error) {
        console.error(`[Cleanup] S3 delete failed for key: ${key}`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          console.log(`[Cleanup] File already deleted or doesn't exist: ${key}`);
          return true; // Consider this a success since the file is gone
        }
        throw s3Error;
      }
    } else {
      // Local file cleanup
      const fileName = previewUrl.split('/').pop();
      const filePath = path.join('./uploads/previews/', fileName);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`[Cleanup] Local preview file deleted successfully: ${filePath}`);
        return true;
      } else {
        console.log(`[Cleanup] Preview file not found, may have been already deleted: ${filePath}`);
        return true;
      }
    }
  } catch (error) {
    console.error(`[Cleanup] Error cleaning up preview file:`, error);
    // Don't throw error - cleanup failure shouldn't prevent content deletion
    return false;
  }
};

/**
 * Validate and extract S3 key from URL
 * @param {string} s3Url - S3 URL to validate
 * @param {string} bucketName - Expected bucket name
 * @returns {string|null} - Extracted S3 key or null if invalid
 */
const validateAndExtractS3Key = (s3Url, bucketName) => {
  try {
    if (!s3Url || !bucketName) {
      console.error('[S3] Missing URL or bucket name');
      return null;
    }

    console.log(`[S3] Validating URL: ${s3Url}`);
    console.log(`[S3] Expected bucket: ${bucketName}`);

    // Check if it's a valid S3 URL
    if (!s3Url.includes('amazonaws.com')) {
      console.error('[S3] URL does not contain amazonaws.com');
      return null;
    }

    let key;
    const urlParts = s3Url.split('/');

    // Method 1: Find bucket name in URL parts
    const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
    if (bucketIndex !== -1) {
      key = urlParts.slice(bucketIndex + 1).join('/');
      console.log(`[S3] Key extracted using bucket name method: ${key}`);
    } else {
      // Method 2: Find amazonaws.com and extract everything after
      const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
      if (domainIndex !== -1) {
        key = urlParts.slice(domainIndex + 1).join('/');
        console.log(`[S3] Key extracted using domain method: ${key}`);
      } else {
        // Method 3: Fallback - assume last 3 parts are the key
        key = urlParts.slice(-3).join('/');
        console.log(`[S3] Key extracted using fallback method: ${key}`);
      }
    }

    // Validate the extracted key
    if (!key || key.length === 0) {
      console.error('[S3] Extracted key is empty');
      return null;
    }

    // Remove any leading slashes
    key = key.replace(/^\/+/, '');

    console.log(`[S3] Final validated key: ${key}`);
    return key;

  } catch (error) {
    console.error('[S3] Error validating S3 URL:', error);
    return null;
  }
};

/**
 * Ensure preview directories exist
 * @returns {void}
 */
const ensurePreviewDirectories = () => {
  try {
    const previewDir = './uploads/previews/';
    const tempDir = './temp/';

    if (!fs.existsSync(previewDir)) {
      console.log(`[Setup] Creating preview directory: ${previewDir}`);
      fs.mkdirSync(previewDir, { recursive: true });
    }

    if (!fs.existsSync(tempDir)) {
      console.log(`[Setup] Creating temp directory: ${tempDir}`);
      fs.mkdirSync(tempDir, { recursive: true });
    }
  } catch (error) {
    console.error('[Setup] Error creating directories:', error);
  }
};

module.exports = {
  generatePreview,
  generateVideoPreview,
  generateVideoPreviewAsync,
  generatePdfPreview,
  canGeneratePreview,
  cleanupPreviewFile,
  ensurePreviewDirectories,
  validateAndExtractS3Key,
  getOptimalDownloadStrategy
};
