const mongoose = require('mongoose');
require('dotenv').config();

const Content = require('../models/Content');

/**
 * Migration script to fix preview URLs that are stored as full S3 URLs
 * instead of S3 keys. This ensures consistent URL handling through the S3UrlHandler middleware.
 */
async function fixPreviewUrls() {
  try {
    console.log('🚀 Starting Preview URL Fix Migration...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI || process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');

    // Find all content with preview URLs that look like full S3 URLs
    console.log('🔍 Finding content with full S3 URLs in previewUrl field...');
    const contentWithFullUrls = await Content.find({
      previewUrl: { 
        $exists: true, 
        $ne: null,
        $ne: '',
        $regex: /^https:\/\/.*amazonaws\.com\//
      }
    }).select('_id title previewUrl');

    console.log(`Found ${contentWithFullUrls.length} content items with full S3 URLs in previewUrl:\n`);

    if (contentWithFullUrls.length === 0) {
      console.log('✅ No content needs preview URL migration. All done!\n');
      return;
    }

    let successCount = 0;
    let failureCount = 0;
    const results = [];

    for (const content of contentWithFullUrls) {
      console.log(`📝 Processing: ${content.title}`);
      console.log(`   Current URL: ${content.previewUrl}`);

      try {
        // Extract S3 key from the full URL
        const s3Key = extractS3KeyFromUrl(content.previewUrl);
        
        if (s3Key) {
          // Update the content with the S3 key instead of full URL
          await Content.findByIdAndUpdate(content._id, { previewUrl: s3Key });
          console.log(`   ✅ Updated to S3 key: ${s3Key}`);
          
          successCount++;
          results.push({
            contentId: content._id,
            title: content.title,
            status: 'success',
            oldUrl: content.previewUrl,
            newKey: s3Key
          });
        } else {
          console.log(`   ⚠️  Could not extract S3 key from URL`);
          failureCount++;
          results.push({
            contentId: content._id,
            title: content.title,
            status: 'failed',
            reason: 'Could not extract S3 key from URL',
            url: content.previewUrl
          });
        }

      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
        failureCount++;
        results.push({
          contentId: content._id,
          title: content.title,
          status: 'error',
          reason: error.message,
          url: content.previewUrl
        });
      }

      console.log(''); // Empty line for readability
    }

    // Summary
    console.log('📊 Migration Summary:');
    console.log(`   ✅ Successfully updated: ${successCount}`);
    console.log(`   ❌ Failed: ${failureCount}`);
    console.log(`   📝 Total processed: ${contentWithFullUrls.length}\n`);

    // Save detailed results to file
    const fs = require('fs');
    const resultsFile = `preview-url-migration-results-${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`📄 Detailed results saved to: ${resultsFile}\n`);

    console.log('🎉 Preview URL migration completed!\n');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('📡 MongoDB connection closed');
  }
}

/**
 * Extract S3 key from a full S3 URL
 * @param {string} s3Url - Full S3 URL
 * @returns {string|null} - S3 key or null if extraction fails
 */
function extractS3KeyFromUrl(s3Url) {
  try {
    const url = new URL(s3Url);
    const pathParts = url.pathname.split('/').filter(part => part.length > 0);

    if (url.hostname.includes('.s3.') || url.hostname.includes('.s3-')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      return pathParts.join('/');
    } else if (url.hostname.startsWith('s3.')) {
      // Format: https://s3.region.amazonaws.com/bucket/key
      return pathParts.slice(1).join('/'); // Remove bucket name
    }

    return null;
  } catch (error) {
    console.error('Error extracting S3 key from URL:', error);
    return null;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  fixPreviewUrls();
}

module.exports = { fixPreviewUrls, extractS3KeyFromUrl };
